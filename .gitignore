# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env*.local

# Build output
dist/
build/
out/
.next/
.nuxt/
.cache/
.parcel-cache/

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Logs
logs/
*.log

# Testing
coverage/
.nyc_output/

# TypeScript
*.tsbuildinfo
.tsc-cache/

# Package manager files
.npm/
.yarn/
.pnp.*
.yarn-integrity

# Docker
docker-compose.override.yml

# Miscellaneous
.eslintcache
.stylelintcache
*.pid
*.seed
*.pid.lock
.grunt/
.lock-wscript
.node_repl_history
.npmrc
.yarnrc

# Temporary files
temp/
tmp/

# Database files
data/*.db
data/*.db-journal
data/*.db.bak
!data/.gitkeep

# Claude Desktop configs (personal)
claude_desktop_config.json
claude_desktop_config_*.json
!claude_desktop_config.example.json

# Personal wrapper scripts
mcp-server-v20.sh
rebuild-v20.sh
!mcp-server-v20.example.sh

# n8n-docs repo (cloned locally)
../n8n-docs/
