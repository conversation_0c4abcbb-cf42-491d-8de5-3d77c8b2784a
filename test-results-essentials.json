{"timestamp": "2025-06-16T09:08:37.822Z", "summary": {"totalTests": 10, "successful": 0, "failed": 10, "averageReduction": null, "totalFullSize": 0, "totalEssentialSize": 0}, "results": [{"nodeType": "nodes-base.httpRequest", "fullSize": 0, "essentialSize": 0, "sizeReduction": 0, "fullPropCount": 0, "essentialPropCount": 0, "success": false, "error": "Unexpected end of JSON input"}, {"nodeType": "nodes-base.webhook", "fullSize": 0, "essentialSize": 0, "sizeReduction": 0, "fullPropCount": 0, "essentialPropCount": 0, "success": false, "error": "Unexpected end of JSON input"}, {"nodeType": "nodes-base.code", "fullSize": 0, "essentialSize": 0, "sizeReduction": 0, "fullPropCount": 0, "essentialPropCount": 0, "success": false, "error": "Unexpected end of JSON input"}, {"nodeType": "nodes-base.set", "fullSize": 0, "essentialSize": 0, "sizeReduction": 0, "fullPropCount": 0, "essentialPropCount": 0, "success": false, "error": "Unexpected end of JSON input"}, {"nodeType": "nodes-base.if", "fullSize": 0, "essentialSize": 0, "sizeReduction": 0, "fullPropCount": 0, "essentialPropCount": 0, "success": false, "error": "Unexpected end of JSON input"}, {"nodeType": "nodes-base.postgres", "fullSize": 0, "essentialSize": 0, "sizeReduction": 0, "fullPropCount": 0, "essentialPropCount": 0, "success": false, "error": "Unexpected token 'o', \"[object Obj\"... is not valid JSON"}, {"nodeType": "nodes-base.openAi", "fullSize": 0, "essentialSize": 0, "sizeReduction": 0, "fullPropCount": 0, "essentialPropCount": 0, "success": false, "error": "\"[object Object]\" is not valid JSON"}, {"nodeType": "nodes-base.googleSheets", "fullSize": 0, "essentialSize": 0, "sizeReduction": 0, "fullPropCount": 0, "essentialPropCount": 0, "success": false, "error": "Unexpected token 'o', \"[object Obj\"... is not valid JSON"}, {"nodeType": "nodes-base.slack", "fullSize": 0, "essentialSize": 0, "sizeReduction": 0, "fullPropCount": 0, "essentialPropCount": 0, "success": false, "error": "Unexpected token 'o', \"[object Obj\"... is not valid JSON"}, {"nodeType": "nodes-base.merge", "fullSize": 0, "essentialSize": 0, "sizeReduction": 0, "fullPropCount": 0, "essentialPropCount": 0, "success": false, "error": "Unexpected end of JSON input"}]}