# Attribution

## Using n8n-MCP in Your Project?

While not legally required, we'd love it if you included attribution! Here are some easy ways:

### In Your README
```
Built with [n8n-MCP](https://github.com/c<PERSON><PERSON><PERSON>/n8n-mcp) by <PERSON><PERSON><PERSON><PERSON> @ [www.aiadvisors.pl/en](https://www.aiadvisors.pl/en)
```

### In Your Documentation
```
This project uses n8n-MCP (https://github.com/c<PERSON><PERSON><PERSON>/n8n-mcp) 
for n8n node documentation access.
Created by <PERSON><PERSON><PERSON><PERSON> @ www.aiadvisors.pl/en
```

### In Code Comments
```javascript
// Powered by n8n-MCP - https://github.com/cz<PERSON><PERSON>/n8n-mcp
// Created by <PERSON><PERSON><PERSON><PERSON> @ www.aiadvisors.pl/en
```

## Why Attribution Matters

Attribution helps:
- Other developers discover this tool
- Build a stronger n8n community
- Me understand how the tool is being used
- You get support and updates

Thank you for using n8n-MCP! 🙏