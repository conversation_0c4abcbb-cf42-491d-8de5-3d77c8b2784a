{"name": "n8n-mcp", "version": "2.4.0", "description": "Integration between n8n workflow automation and Model Context Protocol (MCP)", "main": "dist/index.js", "scripts": {"build": "tsc", "rebuild": "node dist/scripts/rebuild.js", "rebuild:optimized": "node dist/scripts/rebuild-optimized.js", "validate": "node dist/scripts/validate.js", "test-nodes": "node dist/scripts/test-nodes.js", "start": "node dist/mcp/index.js", "start:http": "MCP_MODE=http node dist/mcp/index.js", "start:http:fixed": "MCP_MODE=http USE_FIXED_HTTP=true node dist/mcp/index.js", "start:http:legacy": "MCP_MODE=http node dist/http-server.js", "http": "npm run build && npm run start:http:fixed", "dev": "npm run build && npm run rebuild && npm run validate", "dev:http": "MCP_MODE=http nodemon --watch src --ext ts --exec 'npm run build && npm run start:http'", "test:single-session": "./scripts/test-single-session.sh", "test": "jest", "lint": "tsc --noEmit", "typecheck": "tsc --noEmit", "update:n8n": "node scripts/update-n8n-deps.js", "update:n8n:check": "node scripts/update-n8n-deps.js --dry-run", "db:rebuild": "node dist/scripts/rebuild-database.js", "db:init": "node -e \"new (require('./dist/services/sqlite-storage-service').SQLiteStorageService)(); console.log('Database initialized')\"", "docs:rebuild": "ts-node src/scripts/rebuild-database.ts"}, "repository": {"type": "git", "url": "git+https://github.com/c<PERSON><PERSON><PERSON>/n8n-mcp.git"}, "keywords": ["n8n", "mcp", "model-context-protocol", "ai", "workflow", "automation"], "author": "<PERSON><PERSON><PERSON><PERSON> @ www.aiadvisors.pl/en", "license": "MIT", "bugs": {"url": "https://github.com/cz<PERSON><PERSON>/n8n-mcp/issues"}, "homepage": "https://github.com/cz<PERSON><PERSON>/n8n-mcp#readme", "devDependencies": {"@types/express": "^5.0.3", "@types/jest": "^29.5.14", "@types/node": "^22.15.30", "@types/ws": "^8.18.1", "jest": "^29.7.0", "nodemon": "^3.1.10", "ts-jest": "^29.3.4", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.12.1", "@n8n/n8n-nodes-langchain": "^1.96.1", "better-sqlite3": "^11.10.0", "dotenv": "^16.5.0", "express": "^5.1.0", "n8n": "^1.97.1", "n8n-core": "^1.96.0", "n8n-workflow": "^1.94.0", "sql.js": "^1.13.0"}}