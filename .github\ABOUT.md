# About n8n-MCP

**n8n-MCP** is a Model Context Protocol (MCP) server that gives AI assistants like <PERSON> deep understanding of n8n's 525+ workflow automation nodes.

## 🎯 What it does

- Provides AI assistants with instant access to n8n node documentation, properties, and examples
- Reduces workflow creation time from 45 minutes to 3 minutes (as tested by <PERSON>)
- Eliminates guesswork with accurate node configurations and validation
- Enables AI to build production-ready n8n workflows on the first try

## 🚀 Key Features

- **Smart Search** - Find the right nodes instantly
- **Essential Properties** - Get only what matters (10-20 properties instead of 200+)
- **Task Templates** - Pre-configured settings for common automations
- **Real-time Validation** - Catch errors before deployment
- **Universal Compatibility** - Works with any Node.js version

## 📊 Impact

> "Before MCP, I was translating. Now I'm composing." - Claude

- **6 errors → 0 errors** in workflow creation
- **45 minutes → 3 minutes** development time
- **100% node coverage** with 90% documentation
- **263 AI-capable nodes** fully documented

## 🔧 Use Cases

Perfect for:
- AI assistants building n8n workflows
- Developers learning n8n
- Teams using AI for automation
- Anyone tired of trial-and-error workflow building

## 🏃 Get Started

```bash
# Quick start with Docker
docker run -it ghcr.io/c<PERSON><PERSON><PERSON>/n8n-mcp:latest
```

See the [README](../README.md) for full setup instructions.

---

**Built with ❤️ for the n8n community** | Making AI + n8n workflow creation delightful