# Fly.io configuration for n8n-MCP
# app = "n8n-mcp"  # Will be set during deployment
primary_region = "iad"  # Change to your preferred region

[build]
  dockerfile = "Dockerfile"

[env]
  MCP_MODE = "http"
  USE_FIXED_HTTP = "true"
  NODE_ENV = "production"
  LOG_LEVEL = "info"
  PORT = "3000"
  DISABLE_CONSOLE_OUTPUT = "true"

[http_service]
  internal_port = 3000
  force_https = true
  auto_stop_machines = true
  auto_start_machines = true
  min_machines_running = 0
  processes = ["app"]

  [[http_service.checks]]
    grace_period = "10s"
    interval = "30s"
    method = "GET"
    timeout = "5s"
    path = "/health"

[machine]
  memory = "512mb"
  cpu_kind = "shared"
  cpus = 1

[[vm]]
  memory = "512mb"
  cpu_kind = "shared"
  cpus = 1

[deploy]
  release_command = "echo 'Deployment complete'"

# Persistent storage for the database
[[mounts]]
  source = "n8n_mcp_data"
  destination = "/app/data"
  initial_size = "1gb"
