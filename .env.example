# n8n Documentation MCP Server Configuration

# ====================
# COMMON CONFIGURATION
# ====================

# Database Configuration
# For local development: ./data/nodes.db
# For Docker: /app/data/nodes.db
NODE_DB_PATH=./data/nodes.db

# Logging Level (debug, info, warn, error)
MCP_LOG_LEVEL=info

# Node Environment (development, production)
NODE_ENV=development

# Rebuild database on startup (true/false)
REBUILD_ON_START=false

# =========================
# LOCAL MODE CONFIGURATION
# =========================
# Used when running: npm run start:v2 or npm run dev:v2

# Local MCP Server Configuration
MCP_SERVER_PORT=3000
MCP_SERVER_HOST=localhost
# MCP_AUTH_TOKEN=optional-for-local-development

# =========================
# SIMPLE HTTP MODE
# =========================
# Used for private single-user deployments

# Server mode: stdio (local) or http (remote)
MCP_MODE=stdio

# Use fixed HTTP implementation (recommended for stability)
# Set to true to bypass StreamableHTTPServerTransport issues
USE_FIXED_HTTP=true

# HTTP Server Configuration (only used when MCP_MODE=http)
PORT=3000
HOST=0.0.0.0

# Authentication token for HTTP mode (REQUIRED)
# Generate with: openssl rand -base64 32
AUTH_TOKEN=your-secure-token-here

# CORS origin for HTTP mode (optional)
# Default: * (allow all origins)
# For production, set to your specific domain
# CORS_ORIGIN=https://your-client-domain.com