-- Auto-generated SQL for n8n nodes

-- Node: n8n-nodes-base.Function
INSERT INTO nodes (node_type, name, package_name, code_hash, code_length, source_location, has_credentials)
VALUES ('n8n-nodes-base.Function', 'Function', 'n8n-nodes-base', 'd68f1ab94b190161e2ec2c56ec6631f6c3992826557c100ec578efff5de96a70', 7449, 'node_modules/n8n-nodes-base/dist/nodes/Function/Function.node.js', false);

-- Node: n8n-nodes-base.Webhook
INSERT INTO nodes (node_type, name, package_name, code_hash, code_length, source_location, has_credentials)
VALUES ('n8n-nodes-base.Webhook', 'Webhook', 'n8n-nodes-base', '143d6bbdce335c5a9204112b2c1e8b92e4061d75ba3cb23301845f6fed9e6c71', 10667, 'node_modules/n8n-nodes-base/dist/nodes/Webhook/Webhook.node.js', false);

-- Node: n8n-nodes-base.HttpRequest
INSERT INTO nodes (node_type, name, package_name, code_hash, code_length, source_location, has_credentials)
VALUES ('n8n-nodes-base.HttpRequest', 'HttpRequest', 'n8n-nodes-base', '5b5e2328474b7e85361c940dfe942e167b3f0057f38062f56d6b693f0a7ffe7e', 1343, 'node_modules/n8n-nodes-base/dist/nodes/HttpRequest/HttpRequest.node.js', false);

-- Node: n8n-nodes-base.If
INSERT INTO nodes (node_type, name, package_name, code_hash, code_length, source_location, has_credentials)
VALUES ('n8n-nodes-base.If', 'If', 'n8n-nodes-base', '7910ed9177a946b76f04ca847defb81226c37c698e4cdb63913f038c6c257ee1', 20533, 'node_modules/n8n-nodes-base/dist/nodes/If/If.node.js', false);

-- Node: n8n-nodes-base.SplitInBatches
INSERT INTO nodes (node_type, name, package_name, code_hash, code_length, source_location, has_credentials)
VALUES ('n8n-nodes-base.SplitInBatches', 'SplitInBatches', 'n8n-nodes-base', 'c751422a11e30bf361a6c4803376289740a40434aeb77f90e18cd4dd7ba5c019', 1135, 'node_modules/n8n-nodes-base/dist/nodes/SplitInBatches/SplitInBatches.node.js', false);

-- Node: n8n-nodes-base.Airtable
INSERT INTO nodes (node_type, name, package_name, code_hash, code_length, source_location, has_credentials)
VALUES ('n8n-nodes-base.Airtable', 'Airtable', 'n8n-nodes-base', '2d67e72931697178946f5127b43e954649c4c5e7ad9e29764796404ae96e7db5', 936, 'node_modules/n8n-nodes-base/dist/nodes/Airtable/Airtable.node.js', false);

-- Node: n8n-nodes-base.Slack
INSERT INTO nodes (node_type, name, package_name, code_hash, code_length, source_location, has_credentials)
VALUES ('n8n-nodes-base.Slack', 'Slack', 'n8n-nodes-base', '0ed10d0646f3c595406359edfa2c293dac41991cee59ad4fb3ccf2bb70eca6fc', 1007, 'node_modules/n8n-nodes-base/dist/nodes/Slack/Slack.node.js', false);

-- Node: n8n-nodes-base.Discord
INSERT INTO nodes (node_type, name, package_name, code_hash, code_length, source_location, has_credentials)
VALUES ('n8n-nodes-base.Discord', 'Discord', 'n8n-nodes-base', '4995f9ca5c5b57d2486c2e320cc7505238e7f2260861f7e321b44b45ccabeb00', 10049, 'node_modules/n8n-nodes-base/dist/nodes/Discord/Discord.node.js', false);

