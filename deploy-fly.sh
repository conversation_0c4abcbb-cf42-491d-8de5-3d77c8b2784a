#!/bin/bash
# Deployment script for n8n-MCP to Fly.io

set -e

echo "🚀 Deploying n8n-MCP to Fly.io"
echo "==============================="

# Check if flyctl is installed
if ! command -v flyctl &> /dev/null; then
    echo "❌ flyctl is not installed. Please install it first:"
    echo "   https://fly.io/docs/hands-on/install-flyctl/"
    exit 1
fi

# Check if user is logged in
if ! flyctl auth whoami &> /dev/null; then
    echo "❌ Not logged in to Fly.io. Please run: flyctl auth login"
    exit 1
fi

# Generate AUTH_TOKEN if not set
if [ -z "$AUTH_TOKEN" ]; then
    echo "🔑 Generating AUTH_TOKEN..."
    AUTH_TOKEN=$(openssl rand -base64 32)
    echo "Generated AUTH_TOKEN: $AUTH_TOKEN"
    echo "💾 Save this token for your Claude Desktop configuration!"
fi

# Check if app exists, if not create it
if ! flyctl apps list | grep -q "n8n-mcp"; then
    echo "📱 Creating Fly.io app..."
    flyctl apps create n8n-mcp --generate-name
else
    echo "📱 App 'n8n-mcp' already exists"
fi

# Create volume if it doesn't exist
echo "💾 Creating persistent volume..."
flyctl volumes create n8n_mcp_data --region iad --size 1 || echo "Volume may already exist"

# Set secrets
echo "🔐 Setting secrets..."
flyctl secrets set AUTH_TOKEN="$AUTH_TOKEN"

# Deploy the application
echo "🚀 Deploying application..."
flyctl deploy

# Get the app URL
APP_URL=$(flyctl info --json | jq -r '.Hostname')

echo ""
echo "✅ Deployment complete!"
echo ""
echo "🌐 Your n8n-MCP server is available at:"
echo "   https://$APP_URL"
echo ""
echo "🔍 Health check:"
echo "   https://$APP_URL/health"
echo ""
echo "🤖 Claude Desktop configuration:"
echo "{"
echo "  \"mcpServers\": {"
echo "    \"n8n-mcp-remote\": {"
echo "      \"command\": \"node\","
echo "      \"args\": ["
echo "        \"$(pwd)/scripts/mcp-http-client.js\","
echo "        \"https://$APP_URL/mcp\""
echo "      ],"
echo "      \"env\": {"
echo "        \"MCP_AUTH_TOKEN\": \"$AUTH_TOKEN\""
echo "      }"
echo "    }"
echo "  }"
echo "}"
echo ""
echo "📝 Save the AUTH_TOKEN for future use: $AUTH_TOKEN"
