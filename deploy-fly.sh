#!/bin/bash
# Deployment script for n8n-MCP to Fly.io

set -e

echo "🚀 Deploying n8n-MCP to Fly.io"
echo "==============================="

# Check if flyctl is installed
if ! command -v flyctl &> /dev/null; then
    echo "❌ flyctl is not installed. Please install it first:"
    echo "   https://fly.io/docs/hands-on/install-flyctl/"
    exit 1
fi

# Check if user is logged in
if ! flyctl auth whoami &> /dev/null; then
    echo "❌ Not logged in to Fly.io. Please run: flyctl auth login"
    exit 1
fi

echo "👤 Current Fly.io account: $(flyctl auth whoami)"

# Generate AUTH_TOKEN if not set
if [ -z "$AUTH_TOKEN" ]; then
    echo "🔑 Generating AUTH_TOKEN..."
    AUTH_TOKEN=$(openssl rand -base64 32)
    echo "Generated AUTH_TOKEN: $AUTH_TOKEN"
    echo "💾 Save this token for your Claude Desktop configuration!"
fi

# Check if data/nodes.db exists
if [ ! -f "data/nodes.db" ]; then
    echo "⚠️  Warning: data/nodes.db not found!"
    echo "   Building database first..."
    npm install
    npm run build
    npm run rebuild
fi

# Launch the app (this will create it if it doesn't exist)
echo "📱 Launching Fly.io app..."
flyctl launch --no-deploy --copy-config

# Create volume if it doesn't exist
echo "💾 Creating persistent volume..."
flyctl volumes create n8n_mcp_data --region iad --size 1 || echo "Volume may already exist"

# Set secrets
echo "🔐 Setting secrets..."
flyctl secrets set AUTH_TOKEN="$AUTH_TOKEN"

# Deploy the application
echo "🚀 Deploying application..."
flyctl deploy

# Get the app URL
APP_URL=$(flyctl info --json | jq -r '.Hostname')

echo ""
echo "✅ Deployment complete!"
echo ""
echo "🌐 Your n8n-MCP server is available at:"
echo "   https://$APP_URL"
echo ""
echo "🔍 Health check:"
echo "   https://$APP_URL/health"
echo ""
echo "🤖 Claude Desktop configuration:"
echo "{"
echo "  \"mcpServers\": {"
echo "    \"n8n-mcp-remote\": {"
echo "      \"command\": \"node\","
echo "      \"args\": ["
echo "        \"$(pwd)/scripts/mcp-http-client.js\","
echo "        \"https://$APP_URL/mcp\""
echo "      ],"
echo "      \"env\": {"
echo "        \"MCP_AUTH_TOKEN\": \"$AUTH_TOKEN\""
echo "      }"
echo "    }"
echo "  }"
echo "}"
echo ""
echo "📝 Important: Save this AUTH_TOKEN: $AUTH_TOKEN"
echo ""
echo "📍 Configuration file locations:"
echo "   macOS: ~/Library/Application Support/Claude/claude_desktop_config.json"
echo "   Windows: %APPDATA%\\Claude\\claude_desktop_config.json"
echo "   Linux: ~/.config/Claude/claude_desktop_config.json"
